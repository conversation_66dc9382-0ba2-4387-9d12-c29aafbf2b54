import { router } from '@kit.ArkUI';

// 定义课程成绩数据结构
interface CourseScore {
  courseName: string;
  score: string;
  rank: string;
}

@Entry
@Component
struct ScoreQuery {
  // 模拟数据，提取图片文字填入
  private scoreData: CourseScore[] = [
    { courseName: "高等数学", score: "60", rank: "11" },
    { courseName: "管理学", score: "60", rank: "9" },
    { courseName: "经济法", score: "80", rank: "2" },
    { courseName: "大学英语I", score: "98", rank: "1" },
    { courseName: "公共体育I", score: "88", rank: "1" },
    { courseName: "军事理论与训练", score: "98", rank: "1" }
  ];
  @StorageProp('bottomRectHeight')
  bottomRectHeight:number=0;
  @StorageProp('topRectHeight')
  topRectHeight:number=0;
  build() {
    Column({ space: 0 }) {
      // 标题栏
      Row(){
        Image($r('app.media.fanhui'))
          .onClick(() => router.back())
          .width(20)
          .height(20)
        Text("成绩查询")
          .fontSize(30)
          .fontWeight(700)
          .fontColor('#000000')
          .margin({left:120})

      }
      .width('100%')
      .margin({ bottom: 4, top: 20 });

      Divider()
        .margin({ top: 10,bottom:10})
        .color('#CCCCCC')
        .height(1)
        .width('100%');

      // 表头行（课程名称、分数、班级排名）
      Stack() {
        // 底部背景图
        Image($r("app.media.yjfjiaofei2")) // 替换为实际资源路径
          .objectFit(ImageFit.Cover)
          .width('100%')
          .height(40);

        Row({ space: 0 }) {
          Text("课程名称")
            .fontSize(16)
            .fontWeight(FontWeight.Bold)
            .backgroundColor('#FF9900') // 橙色背景
            .fontColor('#FFFFFF')
            .padding({ top: 8, bottom: 8, left: 16, right: 16 })
            .width('33.33%') // 三列均分宽度
            .textAlign(TextAlign.Center);

          Text("分数")
            .fontSize(16)
            .fontWeight(FontWeight.Bold)
            .backgroundColor('#FF9900')
            .fontColor('#FFFFFF')
            .padding({ top: 8, bottom: 8, left: 16, right: 16 })
            .width('33.33%')
            .textAlign(TextAlign.Center);

          Text("班级排名")
            .fontSize(16)
            .fontWeight(FontWeight.Bold)
            .backgroundColor('#FF9900')
            .fontColor('#FFFFFF')
            .padding({ top: 8, bottom: 8, left: 16, right: 16 })
            .width('33.33%')
            .textAlign(TextAlign.Center);
        }
      }
      .width('90%')
      .margin({ left: '5%', right: '5%' });

      // 数据行循环渲染
      ForEach(this.scoreData, (item: CourseScore) => {
        Row({ space: 0 }) {
          Text(item.courseName)
            .fontSize(15)
            .padding({ top: 10, bottom: 10, left: 16, right: 16 })
            .width('33.33%')
            .textAlign(TextAlign.Center);

          Text(item.score)
            .fontSize(15)
            .padding({ top: 10, bottom: 10, left: 16, right: 16 })
            .width('33.33%')
            .textAlign(TextAlign.Center);

          Text(item.rank)
            .fontSize(15)
            .padding({ top: 10, bottom: 10, left: 16, right: 16 })
            .width('33.33%')
            .textAlign(TextAlign.Center);
        }
        .width('90%')
        .margin({ left: '5%', right: '5%' });
      });

    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
    .padding({top:px2vp(this.topRectHeight),bottom:px2vp(this.bottomRectHeight)})
  }
}