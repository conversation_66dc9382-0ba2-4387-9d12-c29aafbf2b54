import { router } from '@kit.ArkUI';
interface obj_list{
  name:string,
  card:string
  secrete:string
}
interface from_list_type{
  name:string,
  card:string
  secrete:string
}
@Entry
@Component
struct RealName {
  @StorageProp('bottomRectHeight')
  bottomRectHeight:number=0;
  @StorageProp('topRectHeight')
  topRectHeight:number=0;
  @State form_list:from_list_type={
    name:"请填写真实姓名",
    card:"请填写卡号/学号",
    secrete:"请填写校园卡密码"
  }
  list:obj_list={
    name:"姓名",
    card:"卡号/学号",
    secrete:"校园卡密码",
  }

  build() {
    Column() {
      // 标题栏
      Row(){
        Image($r('app.media.fanhui'))
          .onClick(() => router.back())
          .width(20)
          .height(20)
        Text("成绩查询")
          .fontSize(30)
          .fontWeight(700)
          .fontColor('#000000')
          .margin({left:120})

      }
      .width('100%')
      .margin({ bottom: 4});

      // 分割线
      Divider()
        .margin({ top: 10,bottom:10})
        .color('#CCCCCC')
        .height(1)
        .width('100%');


      // 学校（只读）
      Flex({ justifyContent: FlexAlign.SpaceBetween }){
        Text('学校')
        Text('长江大学')
      }.padding(15).border({width:{bottom:1},color:"#f0f0f0"})
      //.padding({ left: 15, right: 20, top: 10 });
      ForEach(Object.keys(this.list),(item:string)=>{
        Flex({ justifyContent: FlexAlign.SpaceBetween }){
          Text(Reflect.get(this.list,item)as string)
          if (item==="name"||item==="card"||item==="secrete") {
            TextInput({text:Reflect.get(this.form_list,item)})
              .backgroundColor(Color.White)
              .fontColor("#969799")
              .width("60%")
              .textAlign(TextAlign.End)
              .padding(0)
              .borderRadius(0)
              .onChange(value=>{
                Reflect.set(this.form_list,item,value)
                console.log("666",JSON.stringify(this.form_list))
              })
          }
          //Text(Reflect.get(this.form_list,item)as string).fontColor("#969799")
        }.padding(15).border({width:{bottom:1},color:"#f0f0f0"})
      })
      // 放在 Column 最后，与 ForEach 同级
      Button('查询')
        .width('90%')
        .height(48)
        .margin({ top: 40 })
        .backgroundColor('#007aff')
        .fontColor('#ffffff')
        .borderRadius(24)
        .onClick(() => {
          if (!this.form_list.name.trim() ||
            !this.form_list.card.trim()  ||
            !this.form_list.secrete.trim()) {
            AlertDialog.show({ message: '请完整填写信息！' });
            return;
          }
          router.pushUrl({
            url: 'pages/qbyycjxq' // 替换为实际要跳转的页面路径
          })
        })
        .width('80%').margin({left:30}).padding(10)
        .backgroundColor('#ff8c00') // 橘色
        .borderRadius(0);      // 直角

    }
    .margin({ top: 10 })
    .height('100%')
    .width('100%')
    .alignItems(HorizontalAlign.Start)
    .padding({top:px2vp(this.topRectHeight),bottom:px2vp(this.bottomRectHeight)})
  }
}