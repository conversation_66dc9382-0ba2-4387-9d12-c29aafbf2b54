// dongtai.ets  动态详情页
import { router } from '@kit.ArkUI';
import { http } from '@kit.NetworkKit';
import prompt from '@ohos.promptAction';

interface PostItem {
  id: number;
  username: string;
  avatar_url?: string;
  content: string;
  image_url?: string;
  created_at: string;
  likes_count: number;
  comments_count: number;
  shares_count: number;
}

interface CommentItem {
  id: number;
  username: string;
  avatar_url?: string;
  content: string;
  created_at: string;
  likes_count: number;
}

@Entry
@Component
struct DongTaiPage {
  @State post: PostItem = JSON.parse((router.getParams() as Record<string, string>)?.post || '{}');
  @State comments: CommentItem[] = [];
  @State commentInput: string = '';
  @State isPostLiked: boolean = false; // 动态点赞状态
  @State likedComments: Set<number> = new Set(); // 评论点赞状态
  @State originalPost: PostItem = JSON.parse((router.getParams() as Record<string, string>)?.post || '{}'); // 保存原始数据

  async aboutToAppear() {
    await this.fetchComments();
    // 保存原始动态数据
    this.originalPost = {
      id: this.post.id,
      username: this.post.username,
      avatar_url: this.post.avatar_url,
      content: this.post.content,
      image_url: this.post.image_url,
      created_at: this.post.created_at,
      likes_count: this.post.likes_count,
      comments_count: this.post.comments_count,
      shares_count: this.post.shares_count,
    };
  }

  private async fetchComments(): Promise<void> {
    try {
      const resp = await http.createHttp().request(
        `http://192.168.31.246:6767/posts/${this.post.id}/comments`,
        { method: http.RequestMethod.GET }
      );
      this.comments = JSON.parse(resp.result as string) as CommentItem[];
    } catch {
      prompt.showToast({ message: '获取评论失败' });
    }
  }

  private async submitComment(): Promise<void> {
    if (!this.commentInput.trim()) {
      prompt.showToast({ message: '请输入评论' });
      return;
    }
    try {
      const resp = await http.createHttp().request(
        `http://192.168.31.246:6767/posts/${this.post.id}/comments`,
        {
          method: http.RequestMethod.POST,
          header: { 'Content-Type': 'application/json' },
          extraData: JSON.stringify({ username: '访客', content: this.commentInput })
        }
      );
      if (resp.responseCode === 200) {
        this.commentInput = '';
        // 更新评论数
        this.post.comments_count++;
        prompt.showToast({ message: '发表成功' });
        await this.fetchComments();

        // 通知首页更新数据
        AppStorage.setOrCreate('shouldRefreshPosts', true);
      }
    } catch {
      prompt.showToast({ message: '发表失败' });
    }
  }

  private async likePost(): Promise<void> {
    // 防止重复点赞
    if (this.isPostLiked) {
      prompt.showToast({ message: '已经点过赞了' });
      return;
    }

    try {
      const resp = await http.createHttp().request(
        `http://192.168.31.246:6767/posts/${this.post.id}/like`,
        { method: http.RequestMethod.POST }
      );
      if (resp.responseCode === 200) {
        this.post.likes_count++;
        this.isPostLiked = true;
        prompt.showToast({ message: '点赞成功' });

        // 通知首页更新数据
        AppStorage.setOrCreate('shouldRefreshPosts', true);
      }
    } catch {
      prompt.showToast({ message: '点赞失败' });
    }
  }

  private async likeComment(cmt: CommentItem, idx: number): Promise<void> {
    // 防止重复点赞
    if (this.likedComments.has(cmt.id)) {
      prompt.showToast({ message: '已经点过赞了' });
      return;
    }

    try {
      const resp = await http.createHttp().request(
        `http://192.168.31.246:6767/comments/${cmt.id}/like`,
        { method: http.RequestMethod.POST }
      );
      if (resp.responseCode === 200) {
        this.comments[idx].likes_count++;
        this.likedComments.add(cmt.id);
        prompt.showToast({ message: '点赞成功' });
      }
    } catch {
      prompt.showToast({ message: '点赞失败' });
    }
  }

  build() {
    Column() {
      /* 顶部导航 */
      Row() {
        Image($r('app.media.fanhui'))
          .width(24)
          .height(24)
          .fillColor('#333333')
          .onClick(() => router.back())

        Text('动态详情')
          .fontSize(18)
          .fontWeight(FontWeight.Medium)
          .fontColor('#333333')
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        // 占位，保持居中
        Text('')
          .width(24)
          .height(24)
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor(Color.White)
      .border({ width: { bottom: 1 }, color: '#f0f0f0' })

      Scroll() {
        Column() {
          /* 用户信息 */
          Row({ space: 12 }) {
            Image($r('app.media.touxiang'))
              .width(50)
              .height(50)
              .borderRadius(25)
              .backgroundColor('#f0f0f0')

            Column({ space: 4 }) {
              Text(this.post.username)
                .fontSize(16)
                .fontWeight(FontWeight.Medium)
                .fontColor('#333333')

              Text(this.post.created_at)
                .fontSize(12)
                .fontColor('#999999')
            }
            .alignItems(HorizontalAlign.Start)
            .layoutWeight(1)
          }
          .width('100%')
          .padding(16)
          .backgroundColor(Color.White)

          /* 正文 */
          Text(this.post.content)
            .fontSize(16)
            .fontColor('#333333')
            .lineHeight(24)
            .padding({ left: 16, right: 16, bottom: 16 })
            .width('100%')
            .backgroundColor(Color.White)

          /* 图片 */
          if (this.post.image_url) {
            Image(this.post.image_url)
              .width('90%')
              .height(200)
              .borderRadius(12)
              .backgroundColor('#f5f5f5')
              .margin({ bottom: 16 })
          }

          /* 点赞/评论/转发 */
          Row() {
            // 点赞
            Row({ space: 8 }) {
              Image($r('app.media.xiai'))
                .width(26)
                .height(26)
                .fillColor(this.isPostLiked ? '#ff4757' : '#666666')
                .onClick(() => this.likePost())
                .animation({
                  duration: 200,
                  curve: Curve.EaseInOut
                })

              Text(this.post.likes_count.toString())
                .fontSize(15)
                .fontColor('#666666')
                .fontWeight(FontWeight.Medium)
            }
            .padding({ left: 12, right: 12, top: 10, bottom: 10 })
            .borderRadius(20)
            .backgroundColor(this.isPostLiked ? '#fff5f5' : '#f8f9fa')

            // 评论
            Row({ space: 8 }) {
              Image($r('app.media.pinglun'))
                .width(26)
                .height(26)
                .fillColor('#666666')

              Text(this.post.comments_count.toString())
                .fontSize(15)
                .fontColor('#666666')
                .fontWeight(FontWeight.Medium)
            }
            .padding({ left: 12, right: 12, top: 10, bottom: 10 })
            .borderRadius(20)
            .backgroundColor('#f8f9fa')

            // 分享
            Row({ space: 8 }) {
              Image($r('app.media.fenxiang'))
                .width(26)
                .height(26)
                .fillColor('#666666')

              Text(this.post.shares_count.toString())
                .fontSize(15)
                .fontColor('#666666')
                .fontWeight(FontWeight.Medium)
            }
            .padding({ left: 12, right: 12, top: 10, bottom: 10 })
            .borderRadius(20)
            .backgroundColor('#f8f9fa')
          }
          .width('100%')
          .justifyContent(FlexAlign.SpaceAround)
          .padding(20)
          .backgroundColor(Color.White)

          Divider()
            .color('#f0f0f0')
            .height(8)

          /* 评论列表 */
          Column() {
            Text(`全部评论 (${this.comments.length})`)
              .fontSize(16)
              .fontWeight(FontWeight.Medium)
              .fontColor('#333333')
              .padding(16)
              .width('100%')
              .textAlign(TextAlign.Start)

            ForEach(this.comments, (cmt: CommentItem, idx: number) => {
              Row({ space: 12 }) {
                Image($r('app.media.touxiang'))
                  .width(42)
                  .height(42)
                  .borderRadius(21)
                  .backgroundColor('#f0f0f0')
                  .border({ width: 2, color: '#99CC33' })

                Column({ space: 8 }) {
                  Text(cmt.username)
                    .fontSize(15)
                    .fontWeight(FontWeight.Medium)
                    .fontColor('#333333')

                  Text(cmt.content)
                    .fontSize(14)
                    .fontColor('#333333')
                    .lineHeight(22)
                    .padding({ top: 4, bottom: 4, left: 12, right: 12 })
                    .backgroundColor('#f8f9fa')
                    .borderRadius(12)

                  Row() {
                    Text(cmt.created_at)
                      .fontSize(12)
                      .fontColor('#999999')

                    Blank()

                    Row({ space: 6 }) {
                      Image($r('app.media.budianzan1'))
                        .width(20)
                        .height(20)
                        .fillColor(this.likedComments.has(cmt.id) ? '#ff4757' : '#999999')
                        .onClick(() => this.likeComment(cmt, idx))
                        .animation({
                          duration: 200,
                          curve: Curve.EaseInOut
                        })

                      Text(cmt.likes_count.toString())
                        .fontSize(12)
                        .fontColor('#999999')
                        .fontWeight(FontWeight.Medium)
                    }
                    .padding({ left: 8, right: 8, top: 4, bottom: 4 })
                    .borderRadius(12)
                    .backgroundColor(this.likedComments.has(cmt.id) ? '#fff5f5' : '#f0f0f0')
                  }
                  .width('100%')
                  .margin({ top: 4 })
                }
                .layoutWeight(1)
                .alignItems(HorizontalAlign.Start)
              }
              .padding({ left: 20, right: 20, top: 16, bottom: 16 })
              .width('100%')
            })
          }
          .backgroundColor(Color.White)
        }
      }
      .layoutWeight(1)

      /* 输入栏 */
      Row({ space: 12 }) {
        TextInput({ placeholder: '文明发言，友善评论...', text: $$this.commentInput })
          .layoutWeight(1)
          .height(44)
          .borderRadius(22)
          .backgroundColor('#f8f9fa')
          .padding({ left: 20, right: 20 })
          .border({ width: 1, color: '#e0e0e0' })
          .fontSize(14)

        Button('发送')
          .height(44)
          .borderRadius(22)
          .backgroundColor('#99CC33')
          .fontColor(Color.White)
          .fontSize(15)
          .fontWeight(FontWeight.Medium)
          .padding({ left: 24, right: 24 })
          .onClick(() => this.submitComment())
          .enabled(this.commentInput.trim().length > 0)
          .opacity(this.commentInput.trim().length > 0 ? 1 : 0.6)
          .shadow({
            radius: 8,
            color: '#1a99CC33',
            offsetX: 0,
            offsetY: 2
          })
      }
      .padding(20)
      .backgroundColor(Color.White)
      .border({ width: { top: 1 }, color: '#f0f0f0' })
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#f8f9fa')
  }
}
